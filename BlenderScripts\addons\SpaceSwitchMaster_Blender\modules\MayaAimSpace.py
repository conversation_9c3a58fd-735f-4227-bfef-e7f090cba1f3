import maya.cmds as cmds


class run():

    def __init__(self, <PERSON>m<PERSON><PERSON><PERSON>, UpAxis, *kwargs):
        
        
        self.AimLocatorCreation(AimAxis, UpAxis)




    def AimLocatorCreation(self, AimAxis, UpAxis):

        Selection = cmds.ls(selection=True)

        RootLocator = cmds.spaceLocator(n=f"{Selection[0]}_Aim_Loc_Root")
        TargetLocator = cmds.spaceLocator(n=f"{Selection[0]}_Aim_Loc_Target")

        cmds.matchTransform(RootLocator, Selection, position=True, rotation=True)
        cmds.matchTransform(TargetLocator, Selection, position=True, rotation=True)

        move_from_axis(TargetLocator, AimAxis)
        self.AimSpaceSwitch(Selection, RootLocator, TargetLocator, AimAxis, UpAxis)
        

    def AimSpaceSwitch(self, Controler, RootLoc, TargetLoc, AimAxis, UpAxis):


        OffsetLocator = cmds.spaceLocator(n="Aim_Loc_Offset")

        start_time = cmds.playbackOptions(q=True, min=True)
        end_time = cmds.playbackOptions(q=True, max=True)

        cmds.matchTransform(OffsetLocator, RootLoc)
        cmds.parent(OffsetLocator, RootLoc)
            
        RootLocatorConstraint = cmds.parentConstraint(Controler, RootLoc, mo = True)
        TargetLocatorConstraint = cmds.parentConstraint(RootLoc, TargetLoc, mo = True)
        cmds.bakeResults(RootLoc, TargetLoc, sm=True, t=(start_time, end_time))
        cmds.delete(RootLocatorConstraint)
        cmds.delete(TargetLocatorConstraint)

        cmds.parentConstraint(OffsetLocator, Controler, mo=True)

        self.aim_constraint(AimAxis, UpAxis, RootLoc[0], OffsetLocator, TargetLoc[0])

        

    def aim_constraint(self, AimAxis, UpAxis, Root, Offset, Target):

        Vectors = aim_vectors(AimAxis, UpAxis)

        cmds.aimConstraint(Target, Offset, mo=True, aimVector=Vectors[0], upVector=Vectors[1], worldUpVector=(0,1,0), worldUpType="objectrotation", worldUpObject=Root)			




def move_from_axis(obj, AimAxis):

    if AimAxis == "+X":
        cmds.move(100,0,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "+Y":
        cmds.move(0,100,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "+Z":
        cmds.move(0,0,100, obj, relative=True, objectSpace=True)
    elif AimAxis == "-X":
        cmds.move(-100,0,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "-Y":
        cmds.move(0,-100,0, obj, relative=True, objectSpace=True)
    else:	
        cmds.move(0,0,-100, obj, relative=True, objectSpace=True)

def aim_vectors(AimAxis, UpAxis):

    AimVector = [1,0,0]
    UpVector = [0,0,1]

    if AimAxis == "+X":
        AimVector = [1,0,0]
    elif AimAxis == "+Y":
        AimVector = [0,1,0]
    elif AimAxis == "+Z":
        AimVector = [0,0,1]
    elif AimAxis == "-X":
        AimVector = [-1,0,0]
    elif AimAxis == "-Y":
        AimVector = [0,-1,0]
    else:	
        AimVector = [0,0,-1]

    if UpAxis == "+X":
        UpVector = [1,0,0]
    elif UpAxis == "+Y":
        UpVector = [0,1,0]
    elif UpAxis == "+Z":
        UpVector = [0,0,1]
    elif UpAxis == "-X":
        UpVector = [-1,0,0]
    elif UpAxis == "-Y":
        UpVector = [0,-1,0]
    else:	
        UpVector = [0,0,-1]

    return AimVector, UpVector