import bpy
from . import version
from .modules import worldSpace, instantWorldSpace, worldLocation, instantWorldLocation

###############
### BUTTONS
###############

class SSM_OT_SpaceSwitch(bpy.types.Operator):
    bl_idname = "ssm.spaceswitch"
    bl_label = "World Space switch"

    def execute(self, context):
        worldSpace.world_space_switch()
        return {'FINISHED'}
    
class SSM_OT_InstantSpaceSwitch(bpy.types.Operator):
    bl_idname = "ssm.instantspaceswitch"
    bl_label = "Instant World Space switch"

    def execute(self, context):
        instantWorldSpace.instant_world_space_switch()
        return {'FINISHED'}
    
class SSM_OT_WorldLocation(bpy.types.Operator):
    bl_idname = "ssm.woldlocation"
    bl_label = "World location"

    def execute(self, context):
        worldLocation.world_location()
        return {'FINISHED'}
    
class SSM_OT_InstantWorldLocation(bpy.types.Operator):
    bl_idname = "ssm.instantwoldlocation"
    bl_label = "instant World location"

    def execute(self, context):
        instantWorldLocation.world_location()
        return {'FINISHED'}


###############
### PANEL
###############

class SSM_PT_Panel(bpy.types.Panel):
    bl_label = version.Name
    bl_idname = "SSM_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "SpaceSwitchMaster"

    def draw(self, context):
        layout = self.layout
        layout.operator("ssm.spaceswitch", text="World Space")
        layout.operator("ssm.instantspaceswitch", text="Instant World Space")
        layout.separator()
        layout.operator("ssm.woldlocation", text="World location")
        layout.operator("ssm.instantwoldlocation", text="Instant World location")

###############
### REGISTER
###############      

classes = [SSM_OT_SpaceSwitch, 
           SSM_OT_InstantSpaceSwitch, 
           SSM_OT_WorldLocation, 
           SSM_OT_InstantWorldLocation, 
           SSM_PT_Panel]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
