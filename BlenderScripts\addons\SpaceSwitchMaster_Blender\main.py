import bpy
from . import version
from .modules import worldSpace, instantWorldSpace, worldLocation, instantWorldLocation, BlenderAimSpace, bake, worldRotationSpace, AimToTarget

###############
### BUTTONS
###############

class SSM_OT_Bake(bpy.types.Operator):
    bl_idname = "ssm.bake"
    bl_label = "Bake selected objects"

    def execute(self, context):
        bake.bake_bones()
        return {'FINISHED'}

class SSM_OT_SpaceSwitch(bpy.types.Operator):
    bl_idname = "ssm.spaceswitch"
    bl_label = "World Space switch"

    def execute(self, context):
        worldSpace.world_space_switch()
        return {'FINISHED'}
    
class SSM_OT_InstantSpaceSwitch(bpy.types.Operator):
    bl_idname = "ssm.instantspaceswitch"
    bl_label = "Instant World Space switch"

    def execute(self, context):
        instantWorldSpace.instant_world_space_switch()
        return {'FINISHED'}

class SSM_OT_RotationSwitch(bpy.types.Operator):
    bl_idname = "ssm.rotationswitch"
    bl_label = "Rotation Switch"

    def execute(self, context):
        worldRotationSpace.world_rotation_bone()
        return {'FINISHED'}
        
class SSM_OT_WorldLocation(bpy.types.Operator):
    bl_idname = "ssm.woldlocation"
    bl_label = "World location"

    def execute(self, context):
        worldLocation.world_location()
        return {'FINISHED'}
    
class SSM_OT_InstantWorldLocation(bpy.types.Operator):
    bl_idname = "ssm.instantwoldlocation"
    bl_label = "instant World location"

    def execute(self, context):
        instantWorldLocation.world_location()
        return {'FINISHED'}

class SSM_OT_AimSpace(bpy.types.Operator):
    bl_idname = "ssm.aimspace"
    bl_label = "Aim Space"
    bl_description = "Create aim space constraint setup"

    aim_axis: bpy.props.EnumProperty(
        name="Aim Axis",
        description="Axis to aim along",
        items=[
            ('+X', '+X', 'Positive X axis'),
            ('+Y', '+Y', 'Positive Y axis'),
            ('+Z', '+Z', 'Positive Z axis'),
            ('-X', '-X', 'Negative X axis'),
            ('-Y', '-Y', 'Negative Y axis'),
            ('-Z', '-Z', 'Negative Z axis'),
        ],
        default='+Z'
    )

    up_axis: bpy.props.EnumProperty(
        name="Up Axis",
        description="Axis to use as up vector",
        items=[
            ('+X', '+X', 'Positive X axis'),
            ('+Y', '+Y', 'Positive Y axis'),
            ('+Z', '+Z', 'Positive Z axis'),
            ('-X', '-X', 'Negative X axis'),
            ('-Y', '-Y', 'Negative Y axis'),
            ('-Z', '-Z', 'Negative Z axis'),
        ],
        default='+Y'
    )

    def execute(self, context):
        BlenderAimSpace.run(self.aim_axis, self.up_axis)
        return {'FINISHED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)

class SSM_OT_AimToTarget_Locator(bpy.types.Operator):
    bl_idname = "ssm.aimtotarget_locator"
    bl_label = "Aim to target Locator"

    def execute(self, context):
        AimToTarget.create_aim_locators()
        return {'FINISHED'}

class SSM_OT_AimToTarget(bpy.types.Operator):
    bl_idname = "ssm.aimtotarget"
    bl_label = "Aim to target"

    def execute(self, context):
        AimToTarget.aim_space_to_target()
        return {'FINISHED'}

###############
### PANEL
###############

class SSM_PT_Panel(bpy.types.Panel):
    bl_label = version.Name
    bl_idname = "SSM_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "SpaceSwitchMaster"

    def draw(self, context):
        layout = self.layout
        layout.operator("ssm.bake", text="Bake")
        layout.separator()
        layout.operator("ssm.spaceswitch", text="World Space")
        layout.operator("ssm.instantspaceswitch", text="Instant World Space")
        layout.operator("ssm.rotationswitch", text="World Rotation")
        layout.separator()
        layout.operator("ssm.woldlocation", text="World location")
        layout.operator("ssm.instantwoldlocation", text="Instant World location")
        layout.separator()
        layout.operator("ssm.aimspace", text="Aim Space")
        layout.separator()
        layout.operator("ssm.aimtotarget_locator", text="Target locator")
        layout.operator("ssm.aimtotarget", text="Aim to locator")

###############
### REGISTER
###############      

classes = [SSM_OT_Bake,
           SSM_OT_SpaceSwitch,
           SSM_OT_InstantSpaceSwitch,
           SSM_OT_RotationSwitch,
           SSM_OT_WorldLocation,
           SSM_OT_InstantWorldLocation,
           SSM_OT_AimSpace,
           SSM_OT_AimToTarget_Locator,
           SSM_OT_AimToTarget,
           SSM_PT_Panel]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
