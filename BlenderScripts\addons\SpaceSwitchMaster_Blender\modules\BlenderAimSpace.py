import bpy


class run():

    def __init__(self, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON>, *kwargs):
        self.AimLocatorCreation(<PERSON>mA<PERSON><PERSON>, UpAxis)

    def AimLocatorCreation(self, <PERSON>mAxi<PERSON>, UpAxis):
        # Get selected objects
        selected_objects = bpy.context.selected_objects
        if not selected_objects:
            print("Please select an object.")
            return

        selection = selected_objects[0]

        # Switch to Object mode if needed
        if bpy.context.mode != 'OBJECT':
            bpy.ops.object.mode_set(mode='OBJECT')
        
        # Create root locator (empty)
        bpy.ops.object.empty_add(type='PLAIN_AXES')
        root_locator = bpy.context.active_object
        root_locator.name = f"{selection.name}_Aim_Loc_Root"
        
        # Create target locator (empty)
        bpy.ops.object.empty_add(type='PLAIN_AXES')
        target_locator = bpy.context.active_object
        target_locator.name = f"{selection.name}_Aim_Loc_Target"
        
        # Match transforms (position and rotation)
        root_locator.location = selection.location.copy()
        root_locator.rotation_euler = selection.rotation_euler.copy()
        
        target_locator.location = selection.location.copy()
        target_locator.rotation_euler = selection.rotation_euler.copy()
        
        # Move target locator along aim axis
        move_from_axis(target_locator, AimAxis)
        
        # Set up aim space switch
        self.AimSpaceSwitch(selection, root_locator, target_locator, AimAxis, UpAxis)

    def AimSpaceSwitch(self, controller, root_loc, target_loc, AimAxis, UpAxis):
        # Create offset locator
        bpy.ops.object.empty_add(type='PLAIN_AXES')
        offset_locator = bpy.context.active_object
        offset_locator.name = "Aim_Loc_Offset"

        # Get timeline range
        scene = bpy.context.scene
        start_frame = scene.frame_start
        end_frame = scene.frame_end

        # Match transform and parent offset to root
        offset_locator.location = root_loc.location.copy()
        offset_locator.rotation_euler = root_loc.rotation_euler.copy()
        offset_locator.parent = root_loc

        # Create constraints for baking
        root_constraint = root_loc.constraints.new(type='COPY_TRANSFORMS')
        root_constraint.target = controller
        root_constraint.use_offset = True

        target_constraint = target_loc.constraints.new(type='COPY_TRANSFORMS')
        target_constraint.target = root_loc
        target_constraint.use_offset = True

        # Bake animation to locators
        bpy.ops.object.select_all(action='DESELECT')
        root_loc.select_set(True)
        target_loc.select_set(True)
        bpy.context.view_layer.objects.active = root_loc

        try:
            bpy.ops.nla.bake(frame_start=start_frame,
                            frame_end=end_frame,
                            only_selected=True,
                            visual_keying=True,
                            clear_constraints=True,
                            use_current_action=True,
                            bake_types={'OBJECT'})
        except:
            # If baking fails, just remove constraints manually
            root_loc.constraints.remove(root_constraint)
            target_loc.constraints.remove(target_constraint)

        # Create parent constraint from controller to offset
        controller_constraint = controller.constraints.new(type='COPY_TRANSFORMS')
        controller_constraint.target = offset_locator
        controller_constraint.use_offset = True

        # Create aim constraint
        self.aim_constraint(AimAxis, UpAxis, offset_locator, target_loc)

    def aim_constraint(self, AimAxis, UpAxis, offset, target):
        # Create Track To constraint (Blender's equivalent of aim constraint)
        track_constraint = offset.constraints.new(type='TRACK_TO')
        track_constraint.target = target

        # Set track axis based on AimAxis
        if AimAxis in ["+X", "-X"]:
            track_constraint.track_axis = 'TRACK_X' if AimAxis == "+X" else 'TRACK_NEGATIVE_X'
        elif AimAxis in ["+Y", "-Y"]:
            track_constraint.track_axis = 'TRACK_Y' if AimAxis == "+Y" else 'TRACK_NEGATIVE_Y'
        else:  # Z axis
            track_constraint.track_axis = 'TRACK_Z' if AimAxis == "+Z" else 'TRACK_NEGATIVE_Z'

        # Set up axis based on UpAxis
        if UpAxis in ["+X", "-X"]:
            track_constraint.up_axis = 'UP_X'
        elif UpAxis in ["+Y", "-Y"]:
            track_constraint.up_axis = 'UP_Y'
        else:  # Z axis
            track_constraint.up_axis = 'UP_Z'


def move_from_axis(obj, AimAxis):
    """Move object along specified axis"""
    if AimAxis == "+X":
        obj.location.x += 10.0
    elif AimAxis == "+Y":
        obj.location.y += 10.0
    elif AimAxis == "+Z":
        obj.location.z += 10.0
    elif AimAxis == "-X":
        obj.location.x -= 10.0
    elif AimAxis == "-Y":
        obj.location.y -= 10.0
    else:  # -Z
        obj.location.z -= 10.0


def aim_vectors(AimAxis, UpAxis):
    """Convert axis strings to vectors (for compatibility)"""
    aim_vector = [1, 0, 0]
    up_vector = [0, 0, 1]
    
    if AimAxis == "+X":
        aim_vector = [1, 0, 0]
    elif AimAxis == "+Y":
        aim_vector = [0, 1, 0]
    elif AimAxis == "+Z":
        aim_vector = [0, 0, 1]
    elif AimAxis == "-X":
        aim_vector = [-1, 0, 0]
    elif AimAxis == "-Y":
        aim_vector = [0, -1, 0]
    else:  # -Z
        aim_vector = [0, 0, -1]
    
    if UpAxis == "+X":
        up_vector = [1, 0, 0]
    elif UpAxis == "+Y":
        up_vector = [0, 1, 0]
    elif UpAxis == "+Z":
        up_vector = [0, 0, 1]
    elif UpAxis == "-X":
        up_vector = [-1, 0, 0]
    elif UpAxis == "-Y":
        up_vector = [0, -1, 0]
    else:  # -Z
        up_vector = [0, 0, -1]
    
    return aim_vector, up_vector
