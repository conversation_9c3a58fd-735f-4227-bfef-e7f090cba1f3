import bpy
from . import worldLocation, instantWorldLocation

TargetLocatorName = "AimToTarget_Locator"

def create_aim_locators():

    try:
        bpy.ops.object.mode_set(mode='OBJECT')
    except:
        pass
    bpy.ops.object.empty_add(type='PLAIN_AXES')
    locator = bpy.context.active_object
    locator.name = TargetLocatorName


def aim_space_to_target():
    armature = bpy.context.object
    if armature.type != 'ARMATURE' or bpy.context.mode != 'POSE':
        print("Please select an armature and be in Pose Mode.")
        return

    scene = bpy.context.scene
    start_frame = scene.frame_start
    end_frame = scene.frame_end

    selected_bone = bpy.context.selected_pose_bones[0]
    selected_bone_name = selected_bone.name

    # Target Locator setup
    target_locator = bpy.data.objects.get(TargetLocatorName)
    if not target_locator:
        print(f"Please create a target locator named {TargetLocatorName}.")
        return
    
    constraint = target_locator.constraints.new(type='CHILD_OF')
    constraint.target = armature
    constraint.subtarget = selected_bone_name

    # Bake the locator's motion
    bpy.ops.object.select_all(action='DESELECT')
    target_locator.select_set(True)
    bpy.context.view_layer.objects.active = locator

    bpy.ops.nla.bake(frame_start=start_frame,
                        frame_end=end_frame,
                        only_selected=True,
                        visual_keying=True,
                        clear_constraints=True,
                        use_current_action=True,
                        bake_types={'OBJECT'})


