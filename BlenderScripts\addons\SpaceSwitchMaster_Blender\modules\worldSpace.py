import bpy

def world_space_switch():
    armature = bpy.context.object
    if armature.type != 'ARMATURE' or bpy.context.mode != 'POSE':
        print("Please select an armature and be in Pose Mode.")
        return

    scene = bpy.context.scene
    start_frame = scene.frame_start
    end_frame = scene.frame_end

    selected_bones = [bone for bone in bpy.context.selected_pose_bones]

    for bone in selected_bones:
        bone_name = bone.name

        # Get the world matrix of the bone's head
        bone_matrix_world = armature.matrix_world @ bone.matrix

        # Switch to Object Mode to add the empty
        bpy.ops.object.mode_set(mode='OBJECT')
        bpy.ops.object.empty_add(type='PLAIN_AXES', location=bone_matrix_world.to_translation())
        locator = bpy.context.active_object
        locator.name = f"{bone_name}_Worldspace_Loc"


        # Constrain the locator to the bone
        constraint = locator.constraints.new(type='COPY_TRANSFORMS')
        constraint.target = armature
        constraint.subtarget = bone_name

        # Bake the locator's motion
        bpy.ops.object.select_all(action='DESELECT')
        locator.select_set(True)
        bpy.context.view_layer.objects.active = locator

        bpy.ops.nla.bake(frame_start=start_frame,
                         frame_end=end_frame,
                         only_selected=True,
                         visual_keying=True,
                         clear_constraints=True,
                         use_current_action=True,
                         bake_types={'OBJECT'})

        # Re-enter Pose Mode and constrain the bone to the baked locator
        bpy.context.view_layer.objects.active = armature
        bpy.ops.object.mode_set(mode='POSE')

        pose_bone = armature.pose.bones[bone_name]
        new_constraint = pose_bone.constraints.new(type='COPY_TRANSFORMS')
        new_constraint.target = locator
