import bpy

def bake_bones():
    armature = bpy.context.object
    if armature.type != 'ARMATURE' or bpy.context.mode != 'POSE':
        print("Please select an armature and be in Pose Mode.")
        return

    scene = bpy.context.scene
    start_frame = scene.frame_start
    end_frame = scene.frame_end

    selected_bones = [bone for bone in bpy.context.selected_pose_bones]

    for bone in selected_bones:
        bpy.ops.nla.bake(frame_start=start_frame,
                        frame_end=end_frame,
                        step=1,
                        only_selected=True,
                        visual_keying=True,
                        clear_constraints=True,
                        use_current_action=True,
                        )

